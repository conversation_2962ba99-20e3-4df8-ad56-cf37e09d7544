directories:
  output: release
  buildResources: build
appId: com.makeshapes.woodpecker-api
productName: Woodpecker API
files:
  - filter:
      - dist/**/*
      - dist-electron/**/*
      - node_modules/**/*
      - package.json
mac:
  target:
    - target: default
      arch:
        - universal
  icon: public/icon.icns
  category: public.app-category.productivity
win:
  target: nsis
  icon: public/icon.ico
linux:
  target: AppImage
  icon: public/icon.png
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
electronVersion: 38.1.0
