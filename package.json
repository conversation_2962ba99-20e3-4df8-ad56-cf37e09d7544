{"name": "woodpecker-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:electron": "vitest run src/main/__tests__ src/preload/__tests__", "test:e2e": "vitest run src/__tests__/e2e.test.ts", "test:workflow": "vitest run src/__tests__/development-workflow.test.ts src/__tests__/build-process.test.ts", "preview": "vite preview", "electron:compile": "vite build --config vite.electron.config.ts", "electron:dev": "npm run electron:compile && electron dist-electron/main.mjs", "electron:build": "npm run build && npm run electron:compile && electron-builder", "electron:test": "npm run build && npm run electron:compile && electron dist-electron/main-test.mjs", "electron:build:dir": "npm run electron:compile && electron-builder --dir", "dev:electron": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && npm run electron:dev\"", "build:electron": "npm run build && npm run electron:build", "dist": "npm run build:electron"}, "dependencies": {"@anthropic-ai/sdk": "^0.62.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/vite": "^4.1.13", "@tiptap/extension-link": "^3.4.2", "@tiptap/extension-placeholder": "^3.4.2", "@tiptap/react": "^3.4.2", "@tiptap/starter-kit": "^3.4.2", "@types/better-sqlite3": "^7.6.13", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.9.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13"}, "devDependencies": {"@eslint/js": "^9.33.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.3.1", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.2", "concurrently": "^9.2.1", "electron": "^38.1.0", "electron-builder": "^26.0.12", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.8", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^6.3.6", "vitest": "^3.2.4", "wait-on": "^8.0.5"}, "main": "dist-electron/main.mjs", "build": {"appId": "com.makeshapes.woodpecker-api", "productName": "Woodpecker API", "directories": {"output": "release"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*", "package.json"], "mac": {"target": [{"target": "default", "arch": ["universal"]}], "icon": "public/icon.icns", "category": "public.app-category.productivity"}, "win": {"target": "nsis", "icon": "public/icon.ico"}, "linux": {"target": "AppImage", "icon": "public/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}