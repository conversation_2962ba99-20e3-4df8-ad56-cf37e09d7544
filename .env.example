# Example environment configuration for Woodpecker API
# Copy this file to .env.local and fill in your actual values

# Development/Production Mode
NODE_ENV=development
ELECTRON_IS_DEV=true

# Database Configuration
DATABASE_PATH=./data/woodpecker.db

# Feature Flags
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ENHANCED_EDITING=false

# Claude API Configuration (Main Process Only - Secure)
# Get your API key from: https://console.anthropic.com/
# IMPORTANT: Use CLAUDE_API_KEY (not VITE_CLAUDE_API_KEY) for security
CLAUDE_API_KEY=your_claude_api_key_here

# Woodpecker API Configuration (Main Process Only - Secure)
# Get your API key from: Woodpecker > Marketplace > Integrations > API keys
# IMPORTANT: Use WOODPECKER_API_KEY (not VITE_WOODPECKER_API_KEY) for security
WOODPECKER_API_KEY=your_woodpecker_api_key_here

# Development Server (automatically set by Vite)
VITE_DEV_SERVER_URL=http://localhost:5173

# SECURITY NOTE:
# - CLAUDE_API_KEY is only accessible by the main Electron process
# - Never use VITE_CLAUDE_API_KEY as it would expose the key to the frontend
# - All Claude API calls are routed through secure IPC channels