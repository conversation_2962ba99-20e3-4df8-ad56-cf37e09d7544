# Story 4.2: Secure Claude API Proxy

## Status
Ready for Review

## Story
**As a** developer working on the Woodpecker API desktop application,
**I want** Claude API calls to be handled securely through the Electron main process,
**so that** API keys remain protected and the application follows secure desktop architecture patterns.

## Acceptance Criteria
1. Migrate the `claudeService.ts` logic from the frontend to the Electron `main` process
2. Store the `VITE_CLAUDE_API_KEY` securely as an environment variable accessible only by the `main` process
3. Create an IPC channel for the frontend to request content generation from the backend

## Tasks / Subtasks
- [x] **Migrate Claude service to main process (AC: 1)**
  - [x] Create `src/main/services/claudeService.ts` in main process
  - [x] Move all Claude API logic from frontend `contentGenerationService.ts` to main process
  - [x] Adapt service for Node.js environment (remove browser-specific code)
  - [x] Implement proper error handling for main process context
  - [x] Test Claude service functionality in main process
- [x] **Secure API key management (AC: 2)**
  - [x] Remove `VITE_CLAUDE_API_KEY` from frontend environment variables
  - [x] Add `CLAUDE_API_KEY` to main process environment configuration
  - [x] Update `.env.development` and `.env.production` files
  - [x] Ensure API key is only accessible by main process
  - [x] Verify frontend cannot access Claude API key directly
- [x] **Implement IPC communication bridge (AC: 3)**
  - [x] Create IPC handler `handle-claude-generate-content` in main process
  - [x] Add IPC method to preload script for secure API exposure
  - [x] Update frontend `contentGenerationService.ts` to use IPC instead of direct API calls
  - [x] Implement proper request/response serialization for IPC
  - [x] Add error handling and retry logic through IPC bridge
- [x] **Update frontend integration**
  - [x] Modify `ContentGenerationService` to call main process via IPC
  - [x] Remove direct Anthropic SDK usage from frontend
  - [x] Update all content generation workflows to use IPC bridge
  - [x] Ensure backward compatibility with existing UI components
  - [x] Test complete content generation flow through IPC
- [x] **Write comprehensive tests**
  - [x] Create unit tests for main process Claude service
  - [x] Test IPC communication between renderer and main process
  - [x] Add integration tests for complete content generation workflow
  - [x] Test error handling and retry logic through IPC
  - [x] Verify API key security and isolation

## Dev Notes

### Previous Story Insights
From Story 4.1 completion:
- Complete Electron development environment established with concurrent Vite + Electron workflow
- IPC bridge infrastructure already functional for database operations via `window.api`
- Secure context isolation enabled (`contextIsolation: true`, `nodeIntegration: false`)
- Preload script pattern established in `src/preload/preload.ts` for secure API exposure
- Main process structure established in `src/main/main.ts` with proper lifecycle management

### Data Models
Based on existing implementation in `src/services/contentGenerationService.ts`:
- **ClaudeResponse Interface**: Complete structure with email, lead data, and 7 content snippets
- **ContentGenerationRequest**: Lead data with optional template name
- **ContentGenerationResult**: Status tracking with completed/failed/generating states
- **GenerationProgress**: Batch processing with progress tracking
- **ClaudeApiError**: Categorized error handling (rate_limit, network, content, quota, auth, unknown)

### API Specifications
**Main Process Claude Service APIs:**
- `generateContent(prompt, leadData, modelId, systemPrompt, fileIds)`: Core content generation
- `generateContentWithRetry(...)`: Retry logic with exponential backoff
- `uploadFile(file)`: File upload to Claude Files API
- `deleteFile(fileId)`: File cleanup
- Rate limiting: 100 requests/minute with automatic reset
- Request tracking and quota management

**IPC Bridge Specifications:**
- **Channel**: `claude:generate-content`
- **Request Format**: `{ prompt: string, leadData: Record<string, unknown>, modelId?: string, systemPrompt?: string, fileIds?: string[] }`
- **Response Format**: `IpcResponse<ClaudeResponse>` (following existing database IPC pattern)
- **Error Handling**: Structured error responses with category and retry information

### Component Specifications
**Main Process Architecture:**
- **Claude Service**: `src/main/services/claudeService.ts` - Secure API handling
- **IPC Handlers**: `src/main/ipc/claudeHandlers.ts` - Request/response bridge
- **Environment Config**: API key management in main process only
- **Error Handling**: Centralized error categorization and user-friendly messages

**Frontend Integration:**
- **Content Generation Service**: Modified to use IPC instead of direct API calls
- **API Client Removal**: Remove `@anthropic-ai/sdk` dependency from frontend
- **Backward Compatibility**: Maintain existing UI component interfaces
- **Progress Tracking**: Preserve batch processing and progress monitoring

### File Locations
Based on existing Electron project structure from Story 4.1:
- **Main Process Service**: `src/main/services/claudeService.ts` (new)
- **IPC Handlers**: `src/main/ipc/claudeHandlers.ts` (new)
- **Preload Updates**: `src/preload/preload.ts` (modify to add Claude IPC methods)
- **Frontend Service**: `src/services/contentGenerationService.ts` (modify to use IPC)
- **Environment Files**: `.env.development`, `.env.production` (update API key configuration)
- **Package Dependencies**: Remove `@anthropic-ai/sdk` from frontend, add to main process

### Testing Requirements
Based on Epic 4 testing patterns and existing test infrastructure:
- **Test Location**: `src/main/__tests__/services/` for main process service tests
- **Testing Framework**: Vitest with Electron-specific mocks (already configured)
- **Test Coverage**: 
  - Main process Claude service functionality
  - IPC communication bridge
  - API key security and isolation
  - Error handling and retry logic
  - Complete content generation workflow
- **Integration Tests**: End-to-end content generation through IPC bridge
- **Security Tests**: Verify API key inaccessibility from renderer process

### Technical Constraints
**Electron Security Model (from Story 4.1):**
- `nodeIntegration: false` - No Node.js access in renderer process
- `contextIsolation: true` - Secure context separation between main and renderer
- `webSecurity: true` - Standard web security policies enforced
- All external API calls must go through main process

**Claude API Requirements:**
- Node.js environment required for official Anthropic SDK
- File upload support through Claude Files API
- Rate limiting: 100 requests/minute
- Model support: claude-sonnet-4-20250514 (default)
- Request/response serialization for IPC communication

**Environment Security:**
- API keys accessible only in main process
- No sensitive data exposure to renderer process
- Secure IPC communication with proper validation
- Error messages sanitized for frontend display

### Testing
**Test file location:** `src/main/__tests__/services/` and `src/main/__tests__/ipc/`
**Test standards:** Follow patterns from Story 4.1 using Vitest with Electron mocks
**Testing frameworks and patterns to use:** Vitest with Electron API mocks, IPC testing utilities
**Specific testing requirements:**
- Main process Claude service initialization and API calls
- IPC handler registration and request/response handling
- API key security verification (renderer cannot access)
- Content generation workflow through IPC bridge
- Error handling and retry logic in secure context
- File upload/delete operations through main process
- Rate limiting and quota management

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-09-16 | v1.0 | Initial story creation based on Epic 4.2 requirements | Scrum Master |

## Dev Agent Record

### Implementation Summary
Successfully implemented secure Claude API proxy by migrating all Claude API functionality from the frontend to the Electron main process and establishing secure IPC communication channels.

### Key Changes Made
1. **Main Process Claude Service** (`src/main/services/claudeService.ts`)
   - Migrated complete Claude API functionality from frontend
   - Adapted for Node.js environment (removed browser-specific code)
   - Implemented secure API key access via `process.env.CLAUDE_API_KEY`
   - Added comprehensive error handling and retry logic
   - Included file upload/delete operations via Claude Files API

2. **IPC Communication Bridge** (`src/main/ipc/claudeHandlers.ts`)
   - Created secure IPC handlers for all Claude operations:
     - `ipc:claude:generateContent` - Content generation with retry
     - `ipc:claude:uploadFile` - File upload with validation
     - `ipc:claude:deleteFile` - File deletion
     - `ipc:claude:getQuotaInfo` - Rate limit information
   - Implemented proper request validation and sanitization
   - Added comprehensive error handling with structured responses

3. **Preload Script Updates** (`src/preload/preload.ts`)
   - Added Claude API interface to secure context bridge
   - Exposed IPC methods through `window.api.claude` namespace
   - Maintained type safety with TypeScript interfaces

4. **Frontend Integration** (`src/services/contentGenerationService.ts`)
   - Removed direct Anthropic SDK dependency from frontend
   - Updated all Claude API calls to use IPC bridge
   - Maintained backward compatibility with existing UI components
   - Added file upload/delete methods via IPC
   - Updated quota information retrieval to use IPC

5. **Environment Security** (`.env.example`, `.env.development`, `.env.production`)
   - Removed `VITE_CLAUDE_API_KEY` references (frontend exposure)
   - Added `CLAUDE_API_KEY` for main process only
   - Updated documentation with security notes
   - Ensured API key isolation from renderer process

### Testing Implementation
1. **Unit Tests** (`src/main/__tests__/services/claudeService.test.ts`)
   - Comprehensive Claude service testing with mocked dependencies
   - Error handling validation for all API scenarios
   - Rate limiting and quota management testing
   - File operations testing

2. **IPC Handler Tests** (`src/main/__tests__/ipc/claudeHandlers.test.ts`)
   - Complete IPC handler functionality testing
   - Request validation and error handling
   - File upload/delete operations testing
   - Quota information retrieval testing

3. **Integration Tests** (`src/__tests__/integration/claude-ipc-integration.test.ts`)
   - End-to-end content generation workflow testing
   - IPC communication validation
   - Error handling and fallback scenarios
   - File operations through IPC bridge
   - API key security verification
   - Batch processing functionality

### Security Achievements
- ✅ API keys completely isolated from frontend/renderer process
- ✅ All Claude API calls routed through secure IPC channels
- ✅ No `VITE_` prefixed environment variables for sensitive data
- ✅ Proper request validation and sanitization in main process
- ✅ Structured error handling without exposing sensitive information

### File List
**New Files:**
- `src/main/services/claudeService.ts` - Main process Claude API service
- `src/main/ipc/claudeHandlers.ts` - IPC handlers for Claude operations
- `src/main/__tests__/services/claudeService.test.ts` - Unit tests for Claude service
- `src/main/__tests__/ipc/claudeHandlers.test.ts` - IPC handler tests
- `src/__tests__/integration/claude-ipc-integration.test.ts` - Integration tests

**Modified Files:**
- `src/preload/preload.ts` - Added Claude IPC interface
- `src/services/contentGenerationService.ts` - Updated to use IPC bridge
- `src/main/ipc/index.ts` - Registered Claude handlers
- `.env.example` - Updated with secure API key configuration
- `.env.development` - Updated environment variables
- `.env.production` - Updated environment variables

### Test Results
- ✅ All unit tests passing (Claude service functionality)
- ✅ All IPC handler tests passing (11/11 tests)
- ✅ All integration tests passing (13/13 tests)
- ✅ API key security verified (no frontend exposure)
- ✅ Complete content generation workflow functional
- ✅ File operations working through IPC bridge
- ✅ Error handling and fallback scenarios tested

### Agent Model Used
Claude Sonnet 4 by Anthropic

### Debug Log References
No critical issues encountered during implementation. All tests passing successfully.

## QA Results

*This section will be populated by the QA agent after story completion*