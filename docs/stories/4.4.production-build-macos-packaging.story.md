# Story 4.4: Production Build & macOS Packaging

## Status
Ready for Development

## Story
**As a** Makeshapes team member,
**I want** the application packaged into a distributable macOS .dmg file with proper branding,
**so that** the Head of Sales can easily install and run the desktop application on their M1 MacBook Air without technical setup.

## Acceptance Criteria
1. Configure `electron-builder` to produce a universal macOS `.dmg` file compatible with both Intel and M1/M2 Apple Silicon chips
2. Implement application icon branding
3. (Optional Stretch Goal) Implement code signing for the application to avoid macOS security warnings

## Tasks / Subtasks
- [ ] **Configure Electron Builder for macOS packaging (AC: 1)**
  - [ ] Install and configure `electron-builder` package
  - [ ] Create build configuration in `package.json` for macOS target
  - [ ] Configure universal binary support for Intel and Apple Silicon
  - [ ] Set up proper app metadata (name, version, description, author)
  - [ ] Configure DMG installer settings (background, icon positioning)
  - [ ] Test build process generates valid .dmg file
- [ ] **Implement application branding and icons (AC: 2)**
  - [ ] Create application icon in required sizes (16x16 to 1024x1024)
  - [ ] Generate .icns file from source icon assets
  - [ ] Configure icon paths in electron-builder configuration
  - [ ] Set up DMG background image and installer appearance
  - [ ] Verify icon displays correctly in Finder and Dock
  - [ ] Test application branding appears in About dialog
- [ ] **Production build optimization and testing**
  - [ ] Configure production build scripts in package.json
  - [ ] Optimize bundle size and remove development dependencies
  - [ ] Set up proper environment variable handling for production
  - [ ] Create build verification script to test .dmg installation
  - [ ] Test complete installation and launch process on clean macOS system
  - [ ] Verify all application features work in packaged version
- [ ] **Optional: Code signing implementation (AC: 3)**
  - [ ] Research Apple Developer account requirements for code signing
  - [ ] Configure code signing certificates if available
  - [ ] Set up notarization process for macOS Gatekeeper
  - [ ] Test signed application installation without security warnings
  - [ ] Document code signing setup for future maintenance
- [ ] **Documentation and deployment preparation**
  - [ ] Create installation instructions for end users
  - [ ] Document build process for developers
  - [ ] Set up release workflow for generating distribution builds
  - [ ] Create troubleshooting guide for common installation issues
  - [ ] Verify final .dmg meets all Epic 4 requirements

## Dev Notes

### Previous Story Insights
From Stories 4.1-4.3 completion:
- Complete Electron application shell established with secure main/renderer architecture
- All external API calls (Claude, Woodpecker) successfully migrated to main process with IPC bridge
- SQLite database integration functional with proper data persistence
- Development workflow established with concurrent Vite + Electron execution
- Security model proven with `contextIsolation: true` and `nodeIntegration: false`
- All application features from Epic 3 confirmed working in Electron environment

### Data Models
No specific data models required for packaging - this story focuses on build configuration and distribution.

### API Specifications
No new API specifications - packaging uses existing Electron APIs and build tools.

### Component Specifications
**Build Configuration:**
- **Electron Builder Config**: Complete macOS build configuration in package.json
- **Icon Assets**: Multi-resolution icon set (.icns format for macOS)
- **DMG Installer**: Custom installer appearance with branding
- **Universal Binary**: Support for both Intel and Apple Silicon architectures

**Production Optimization:**
- **Bundle Analysis**: Minimize final application size
- **Environment Handling**: Proper production environment variable management
- **Asset Optimization**: Compress and optimize all included assets

### File Locations
Based on established Electron project structure from Stories 4.1-4.3:
- **Build Configuration**: `package.json` (add build section)
- **Icon Assets**: `assets/icons/` (new directory)
- **Build Scripts**: `scripts/build.js` (optional build helper)
- **Distribution Output**: `dist/` (electron-builder output)
- **Documentation**: `docs/deployment/` (installation and build docs)

### Technical Constraints
**Electron Builder Requirements:**
- Node.js 16+ required for electron-builder
- macOS development machine recommended for testing .dmg creation
- Sufficient disk space for universal binary compilation
- Apple Developer account required for code signing (optional)

**macOS Compatibility:**
- Target macOS 10.14+ for broad compatibility
- Universal binary supports both Intel and Apple Silicon
- DMG installer follows macOS Human Interface Guidelines
- Application bundle structure meets Apple requirements

**Build Performance:**
- Universal binary compilation increases build time significantly
- Large bundle size due to Electron runtime and Node.js dependencies
- Consider build caching for development workflow optimization

### Testing
**Test approach:** Manual testing on target macOS systems with automated build verification
**Testing requirements:**
- Build process completes successfully without errors
- Generated .dmg file opens and installs correctly
- Application launches and all features work in packaged version
- Icon and branding display correctly throughout macOS
- Installation works on both Intel and Apple Silicon Macs
- Uninstallation process works cleanly
- Performance matches development version

**Test environments:**
- macOS 12+ on Apple Silicon (primary target)
- macOS 10.14+ on Intel (compatibility verification)
- Clean macOS systems without development tools

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-09-16 | v1.0 | Initial story creation based on Epic 4.4 requirements | Scrum Master |

## Dev Agent Record

*This section will be populated by the development agent during implementation*

## QA Results

*This section will be populated by the QA agent after story completion*