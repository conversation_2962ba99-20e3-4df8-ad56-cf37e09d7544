{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "CommonJS", "moduleResolution": "node", "outDir": "dist-electron", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "declaration": false, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["node", "electron"]}, "include": ["src/main/**/*", "src/preload/**/*", "src/database/**/*", "src/types/**/*"], "exclude": ["node_modules", "dist", "dist-electron", "src/**/*.test.ts", "src/**/*.test.tsx", "src/**/__tests__/**/*"]}