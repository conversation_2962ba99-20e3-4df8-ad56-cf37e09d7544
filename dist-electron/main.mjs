import { ipc<PERSON>ain, app, dialog, BrowserWindow, shell, Menu } from "electron";
import { fileURLToPath } from "node:url";
import path$1 from "node:path";
import Database from "better-sqlite3";
import path from "path";
import fs from "fs";
import { l as logger } from "./logger-CDSbv2DK.js";
const CREATE_TABLES_SQL = {
  imports: `
    CREATE TABLE IF NOT EXISTS imports (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      filename TEXT NOT NULL,
      import_date DATETIME DEFAULT CURRENT_TIMESTAMP,
      status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
      lead_count INTEGER DEFAULT 0,
      error_messages TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `,
  leads: `
    CREATE TABLE IF NOT EXISTS leads (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      import_id INTEGER NOT NULL,
      company TEXT,
      contact_name TEXT,
      email TEXT,
      title TEXT,
      additional_fields TEXT, -- JSON string
      status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'exported', 'failed')),
      woodpecker_campaign_id TEXT,
      export_date DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (import_id) REFERENCES imports(id) ON DELETE CASCADE
    )
  `,
  generated_content: `
    CREATE TABLE IF NOT EXISTS generated_content (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      lead_id INTEGER NOT NULL,
      touchpoint_number INTEGER NOT NULL,
      content TEXT NOT NULL,
      content_type TEXT NOT NULL CHECK (content_type IN ('email', 'subject', 'template')),
      template_id TEXT,
      status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'approved', 'rejected')),
      generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      approved_at DATETIME,
      FOREIGN KEY (lead_id) REFERENCES leads(id) ON DELETE CASCADE
    )
  `,
  mappings: `
    CREATE TABLE IF NOT EXISTS mappings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      import_id INTEGER NOT NULL,
      csv_column TEXT NOT NULL,
      woodpecker_field TEXT NOT NULL,
      mapping_type TEXT DEFAULT 'direct' CHECK (mapping_type IN ('direct', 'computed', 'default')),
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (import_id) REFERENCES imports(id) ON DELETE CASCADE
    )
  `,
  app_metadata: `
    CREATE TABLE IF NOT EXISTS app_metadata (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `
};
const CREATE_INDEXES_SQL = [
  "CREATE INDEX IF NOT EXISTS idx_leads_import_id ON leads(import_id)",
  "CREATE INDEX IF NOT EXISTS idx_leads_email ON leads(email)",
  "CREATE INDEX IF NOT EXISTS idx_leads_status ON leads(status)",
  "CREATE INDEX IF NOT EXISTS idx_generated_content_lead_id ON generated_content(lead_id)",
  "CREATE INDEX IF NOT EXISTS idx_generated_content_status ON generated_content(status)",
  "CREATE INDEX IF NOT EXISTS idx_mappings_import_id ON mappings(import_id)",
  "CREATE INDEX IF NOT EXISTS idx_imports_status ON imports(status)",
  "CREATE INDEX IF NOT EXISTS idx_imports_date ON imports(import_date)"
];
const INITIAL_METADATA = [
  { key: "schema_version", value: "1.0.0" },
  { key: "created_at", value: (/* @__PURE__ */ new Date()).toISOString() },
  { key: "last_migration", value: "1.0.0" }
];
let electronApp = null;
try {
  electronApp = require("electron")?.app;
} catch {
}
function getDatabasePath() {
  if (electronApp && electronApp.getPath) {
    const userDataPath = electronApp.getPath("userData");
    return path.join(userDataPath, "leads.db");
  }
  return path.join(process.cwd(), "leads.db");
}
function initializeDatabase() {
  const dbPath = getDatabasePath();
  const dbDir = path.dirname(dbPath);
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
  }
  try {
    const db = new Database(dbPath);
    db.pragma("foreign_keys = ON");
    db.pragma("journal_mode = WAL");
    const createTables = db.transaction(() => {
      Object.values(CREATE_TABLES_SQL).forEach((sql) => {
        db.exec(sql);
      });
      CREATE_INDEXES_SQL.forEach((sql) => {
        db.exec(sql);
      });
      const checkMetadata = db.prepare("SELECT COUNT(*) as count FROM app_metadata");
      const metadataCount = checkMetadata.get();
      if (metadataCount.count === 0) {
        const insertMetadata = db.prepare("INSERT INTO app_metadata (key, value) VALUES (?, ?)");
        INITIAL_METADATA.forEach(({ key, value }) => {
          insertMetadata.run(key, value);
        });
      }
    });
    createTables();
    return db;
  } catch (error) {
    throw new Error(`Failed to initialize database: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}
function isDatabaseInitialized() {
  const dbPath = getDatabasePath();
  if (!fs.existsSync(dbPath)) {
    return false;
  }
  try {
    const db = new Database(dbPath, { readonly: true });
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN ('imports', 'leads', 'generated_content', 'mappings', 'app_metadata')
    `).all();
    db.close();
    return tables.length === 5;
  } catch {
    return false;
  }
}
function getDatabase() {
  if (!isDatabaseInitialized()) {
    return initializeDatabase();
  }
  const dbPath = getDatabasePath();
  const db = new Database(dbPath);
  db.pragma("foreign_keys = ON");
  return db;
}
function closeDatabase(db) {
  try {
    if (db && typeof db.close === "function") {
      db.close();
    }
  } catch (error) {
    console.error("Error closing database:", error);
  }
}
class DatabasePool {
  connections = [];
  maxConnections = 5;
  getConnection() {
    if (this.connections.length > 0) {
      return this.connections.pop();
    }
    return getDatabase();
  }
  releaseConnection(db) {
    if (this.connections.length < this.maxConnections) {
      this.connections.push(db);
    } else {
      closeDatabase(db);
    }
  }
  closeAll() {
    this.connections.forEach((db) => closeDatabase(db));
    this.connections = [];
  }
}
const dbPool = new DatabasePool();
function withDatabase(operation) {
  const db = dbPool.getConnection();
  try {
    return operation(db);
  } finally {
    dbPool.releaseConnection(db);
  }
}
function withTransaction(operation) {
  return withDatabase((db) => {
    const transaction = db.transaction(() => operation(db));
    return transaction();
  });
}
function cleanup() {
  dbPool.closeAll();
}
if (typeof process !== "undefined") {
  process.on("exit", cleanup);
  process.on("SIGINT", cleanup);
  process.on("SIGTERM", cleanup);
}
class ImportsDAL {
  static create(importData) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        INSERT INTO imports (filename, import_date, status, lead_count, error_messages)
        VALUES (?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        importData.filename,
        importData.import_date || (/* @__PURE__ */ new Date()).toISOString(),
        importData.status,
        importData.lead_count || 0,
        importData.error_messages || null
      );
      return this.getById(result.lastInsertRowid);
    });
  }
  static getById(id) {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT * FROM imports WHERE id = ?");
      return stmt.get(id) || null;
    });
  }
  static getAll(filters, pagination) {
    return withDatabase((db) => {
      let query = "SELECT * FROM imports WHERE 1=1";
      const params = [];
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.filename) {
        query += " AND filename LIKE ?";
        params.push(`%${filters.filename}%`);
      }
      if (filters?.dateFrom) {
        query += " AND import_date >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND import_date <= ?";
        params.push(filters.dateTo);
      }
      query += " ORDER BY import_date DESC";
      if (pagination?.limit) {
        query += " LIMIT ?";
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += " OFFSET ?";
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static update(id, updates) {
    return withDatabase((db) => {
      const fields = Object.keys(updates).filter((key) => updates[key] !== void 0);
      if (fields.length === 0) return this.getById(id);
      const setClause = fields.map((field) => `${field} = ?`).join(", ");
      const values = fields.map((field) => updates[field]);
      const stmt = db.prepare(`UPDATE imports SET ${setClause} WHERE id = ?`);
      const result = stmt.run(...values, id);
      return result.changes > 0 ? this.getById(id) : null;
    });
  }
  static updateStatus(id, status, errorMessages) {
    return this.update(id, { status, error_messages: errorMessages });
  }
  static updateLeadCount(id, leadCount) {
    return this.update(id, { lead_count: leadCount });
  }
  static delete(id) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM imports WHERE id = ?");
      const result = stmt.run(id);
      return result.changes > 0;
    });
  }
  static getCount(filters) {
    return withDatabase((db) => {
      let query = "SELECT COUNT(*) as count FROM imports WHERE 1=1";
      const params = [];
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.filename) {
        query += " AND filename LIKE ?";
        params.push(`%${filters.filename}%`);
      }
      if (filters?.dateFrom) {
        query += " AND import_date >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND import_date <= ?";
        params.push(filters.dateTo);
      }
      const stmt = db.prepare(query);
      const result = stmt.get(...params);
      return result.count;
    });
  }
  static getByStatus(status) {
    return this.getAll({ status });
  }
  static getRecent(limit = 10) {
    return this.getAll({}, { limit });
  }
}
class LeadsDAL {
  static create(leadData) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        INSERT INTO leads (
          import_id, company, contact_name, email, title, 
          additional_fields, status, woodpecker_campaign_id, export_date
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        leadData.import_id,
        leadData.company || null,
        leadData.contact_name || null,
        leadData.email || null,
        leadData.title || null,
        leadData.additional_fields || null,
        leadData.status || "pending",
        leadData.woodpecker_campaign_id || null,
        leadData.export_date || null
      );
      return this.getById(result.lastInsertRowid);
    });
  }
  static bulkCreate(bulkData) {
    return withTransaction((db) => {
      const stmt = db.prepare(`
        INSERT INTO leads (
          import_id, company, contact_name, email, title, 
          additional_fields, status, woodpecker_campaign_id, export_date
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      const results = [];
      for (const lead of bulkData.leads) {
        const result = stmt.run(
          bulkData.import_id,
          lead.company || null,
          lead.contact_name || null,
          lead.email || null,
          lead.title || null,
          lead.additional_fields || null,
          lead.status || "pending",
          lead.woodpecker_campaign_id || null,
          lead.export_date || null
        );
        const created = this.getById(result.lastInsertRowid);
        if (created) results.push(created);
      }
      return results;
    });
  }
  static getById(id) {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT * FROM leads WHERE id = ?");
      return stmt.get(id) || null;
    });
  }
  static getAll(filters, pagination) {
    return withDatabase((db) => {
      let query = "SELECT * FROM leads WHERE 1=1";
      const params = [];
      if (filters?.import_id) {
        query += " AND import_id = ?";
        params.push(filters.import_id);
      }
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.company) {
        query += " AND company LIKE ?";
        params.push(`%${filters.company}%`);
      }
      if (filters?.email) {
        query += " AND email LIKE ?";
        params.push(`%${filters.email}%`);
      }
      if (filters?.contact_name) {
        query += " AND contact_name LIKE ?";
        params.push(`%${filters.contact_name}%`);
      }
      if (filters?.woodpecker_campaign_id) {
        query += " AND woodpecker_campaign_id = ?";
        params.push(filters.woodpecker_campaign_id);
      }
      if (filters?.dateFrom) {
        query += " AND created_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND created_at <= ?";
        params.push(filters.dateTo);
      }
      query += " ORDER BY created_at DESC";
      if (pagination?.limit) {
        query += " LIMIT ?";
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += " OFFSET ?";
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static update(id, updates) {
    return withDatabase((db) => {
      const fields = Object.keys(updates).filter((key) => updates[key] !== void 0);
      if (fields.length === 0) return this.getById(id);
      const setClause = fields.map((field) => `${field} = ?`).join(", ");
      const values = fields.map((field) => updates[field]);
      const stmt = db.prepare(`UPDATE leads SET ${setClause} WHERE id = ?`);
      const result = stmt.run(...values, id);
      return result.changes > 0 ? this.getById(id) : null;
    });
  }
  static updateStatus(id, status) {
    return this.update(id, { status });
  }
  static updateWoodpeckerCampaign(id, campaignId, exportDate) {
    return this.update(id, {
      woodpecker_campaign_id: campaignId,
      export_date: exportDate || (/* @__PURE__ */ new Date()).toISOString(),
      status: "exported"
    });
  }
  static bulkUpdateStatus(ids, status) {
    return withTransaction((db) => {
      const placeholders = ids.map(() => "?").join(",");
      const stmt = db.prepare(`UPDATE leads SET status = ? WHERE id IN (${placeholders})`);
      const result = stmt.run(status, ...ids);
      return result.changes;
    });
  }
  static delete(id) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM leads WHERE id = ?");
      const result = stmt.run(id);
      return result.changes > 0;
    });
  }
  static deleteByImport(importId) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM leads WHERE import_id = ?");
      const result = stmt.run(importId);
      return result.changes;
    });
  }
  static getCount(filters) {
    return withDatabase((db) => {
      let query = "SELECT COUNT(*) as count FROM leads WHERE 1=1";
      const params = [];
      if (filters?.import_id) {
        query += " AND import_id = ?";
        params.push(filters.import_id);
      }
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.company) {
        query += " AND company LIKE ?";
        params.push(`%${filters.company}%`);
      }
      if (filters?.email) {
        query += " AND email LIKE ?";
        params.push(`%${filters.email}%`);
      }
      if (filters?.contact_name) {
        query += " AND contact_name LIKE ?";
        params.push(`%${filters.contact_name}%`);
      }
      if (filters?.woodpecker_campaign_id) {
        query += " AND woodpecker_campaign_id = ?";
        params.push(filters.woodpecker_campaign_id);
      }
      if (filters?.dateFrom) {
        query += " AND created_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND created_at <= ?";
        params.push(filters.dateTo);
      }
      const stmt = db.prepare(query);
      const result = stmt.get(...params);
      return result.count;
    });
  }
  static getByImport(importId, pagination) {
    return this.getAll({ import_id: importId }, pagination);
  }
  static getByStatus(status, pagination) {
    return this.getAll({ status }, pagination);
  }
  static searchByEmail(email) {
    return this.getAll({ email });
  }
  static searchByCompany(company, pagination) {
    return this.getAll({ company }, pagination);
  }
  static getExportedLeads(campaignId) {
    const filters = { status: "exported" };
    if (campaignId) {
      filters.woodpecker_campaign_id = campaignId;
    }
    return this.getAll(filters);
  }
  static getLeadsWithAdditionalFields() {
    return withDatabase((db) => {
      const stmt = db.prepare('SELECT * FROM leads WHERE additional_fields IS NOT NULL AND additional_fields != "" ORDER BY created_at DESC');
      return stmt.all();
    });
  }
}
class GeneratedContentDAL {
  static create(contentData) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        INSERT INTO generated_content (
          lead_id, touchpoint_number, content, content_type, 
          template_id, status, approved_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        contentData.lead_id,
        contentData.touchpoint_number,
        contentData.content,
        contentData.content_type,
        contentData.template_id || null,
        contentData.status || "draft",
        contentData.approved_at || null
      );
      return this.getById(result.lastInsertRowid);
    });
  }
  static getById(id) {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT * FROM generated_content WHERE id = ?");
      return stmt.get(id) || null;
    });
  }
  static getAll(filters, pagination) {
    return withDatabase((db) => {
      let query = "SELECT * FROM generated_content WHERE 1=1";
      const params = [];
      if (filters?.lead_id) {
        query += " AND lead_id = ?";
        params.push(filters.lead_id);
      }
      if (filters?.touchpoint_number) {
        query += " AND touchpoint_number = ?";
        params.push(filters.touchpoint_number);
      }
      if (filters?.content_type) {
        query += " AND content_type = ?";
        params.push(filters.content_type);
      }
      if (filters?.template_id) {
        query += " AND template_id = ?";
        params.push(filters.template_id);
      }
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.dateFrom) {
        query += " AND generated_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND generated_at <= ?";
        params.push(filters.dateTo);
      }
      query += " ORDER BY generated_at DESC";
      if (pagination?.limit) {
        query += " LIMIT ?";
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += " OFFSET ?";
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static update(id, updates) {
    return withDatabase((db) => {
      const fields = Object.keys(updates).filter((key) => updates[key] !== void 0);
      if (fields.length === 0) return this.getById(id);
      const setClause = fields.map((field) => `${field} = ?`).join(", ");
      const values = fields.map((field) => updates[field]);
      const stmt = db.prepare(`UPDATE generated_content SET ${setClause} WHERE id = ?`);
      const result = stmt.run(...values, id);
      return result.changes > 0 ? this.getById(id) : null;
    });
  }
  static updateStatus(id, status) {
    const updates = { status };
    if (status === "approved") {
      updates.approved_at = (/* @__PURE__ */ new Date()).toISOString();
    } else if (status === "draft" || status === "rejected") {
      updates.approved_at = null;
    }
    return this.update(id, updates);
  }
  static updateContent(id, content) {
    return this.update(id, { content, status: "draft", approved_at: null });
  }
  static delete(id) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM generated_content WHERE id = ?");
      const result = stmt.run(id);
      return result.changes > 0;
    });
  }
  static deleteByLead(leadId) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM generated_content WHERE lead_id = ?");
      const result = stmt.run(leadId);
      return result.changes;
    });
  }
  static getCount(filters) {
    return withDatabase((db) => {
      let query = "SELECT COUNT(*) as count FROM generated_content WHERE 1=1";
      const params = [];
      if (filters?.lead_id) {
        query += " AND lead_id = ?";
        params.push(filters.lead_id);
      }
      if (filters?.touchpoint_number) {
        query += " AND touchpoint_number = ?";
        params.push(filters.touchpoint_number);
      }
      if (filters?.content_type) {
        query += " AND content_type = ?";
        params.push(filters.content_type);
      }
      if (filters?.template_id) {
        query += " AND template_id = ?";
        params.push(filters.template_id);
      }
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.dateFrom) {
        query += " AND generated_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND generated_at <= ?";
        params.push(filters.dateTo);
      }
      const stmt = db.prepare(query);
      const result = stmt.get(...params);
      return result.count;
    });
  }
  static getByLead(leadId, pagination) {
    return this.getAll({ lead_id: leadId }, pagination);
  }
  static getByTouchpoint(leadId, touchpointNumber) {
    return this.getAll({ lead_id: leadId, touchpoint_number: touchpointNumber });
  }
  static getByStatus(status, pagination) {
    return this.getAll({ status }, pagination);
  }
  static getByContentType(contentType, pagination) {
    return this.getAll({ content_type: contentType }, pagination);
  }
  static getByTemplate(templateId, pagination) {
    return this.getAll({ template_id: templateId }, pagination);
  }
  static getApprovedContent(leadId) {
    const filters = { status: "approved" };
    if (leadId) {
      filters.lead_id = leadId;
    }
    return this.getAll(filters);
  }
  static getPendingApproval(pagination) {
    return this.getAll({ status: "draft" }, pagination);
  }
  static getLeadSequence(leadId) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        SELECT * FROM generated_content 
        WHERE lead_id = ? 
        ORDER BY touchpoint_number ASC, content_type ASC
      `);
      return stmt.all(leadId);
    });
  }
  static getContentStats() {
    return withDatabase((db) => {
      const totalStmt = db.prepare("SELECT COUNT(*) as count FROM generated_content");
      const total = totalStmt.get().count;
      const statusStmt = db.prepare("SELECT status, COUNT(*) as count FROM generated_content GROUP BY status");
      const statusResults = statusStmt.all();
      const byStatus = statusResults.reduce((acc, row) => {
        acc[row.status] = row.count;
        return acc;
      }, {});
      const typeStmt = db.prepare("SELECT content_type, COUNT(*) as count FROM generated_content GROUP BY content_type");
      const typeResults = typeStmt.all();
      const byType = typeResults.reduce((acc, row) => {
        acc[row.content_type] = row.count;
        return acc;
      }, {});
      return { total, byStatus, byType };
    });
  }
  static bulkUpdateStatus(ids, status) {
    return withTransaction((db) => {
      const placeholders = ids.map(() => "?").join(",");
      let query = `UPDATE generated_content SET status = ?`;
      const params = [status];
      if (status === "approved") {
        query += `, approved_at = ?`;
        params.push((/* @__PURE__ */ new Date()).toISOString());
      } else if (status === "draft" || status === "rejected") {
        query += `, approved_at = NULL`;
      }
      query += ` WHERE id IN (${placeholders})`;
      params.push(...ids);
      const stmt = db.prepare(query);
      const result = stmt.run(...params);
      return result.changes;
    });
  }
}
class MappingsDAL {
  static create(mappingData) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        INSERT INTO mappings (
          import_id, csv_column, woodpecker_field, mapping_type, is_active
        )
        VALUES (?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        mappingData.import_id,
        mappingData.csv_column,
        mappingData.woodpecker_field,
        mappingData.mapping_type || "direct",
        mappingData.is_active !== void 0 ? mappingData.is_active : true
      );
      return this.getById(result.lastInsertRowid);
    });
  }
  static bulkCreate(bulkData) {
    return withTransaction((db) => {
      const stmt = db.prepare(`
        INSERT INTO mappings (
          import_id, csv_column, woodpecker_field, mapping_type, is_active
        )
        VALUES (?, ?, ?, ?, ?)
      `);
      const results = [];
      for (const mapping of bulkData.mappings) {
        const result = stmt.run(
          bulkData.import_id,
          mapping.csv_column,
          mapping.woodpecker_field,
          mapping.mapping_type || "direct",
          mapping.is_active !== void 0 ? mapping.is_active : true
        );
        const created = this.getById(result.lastInsertRowid);
        if (created) results.push(created);
      }
      return results;
    });
  }
  static getById(id) {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT * FROM mappings WHERE id = ?");
      return stmt.get(id) || null;
    });
  }
  static getAll(filters, pagination) {
    return withDatabase((db) => {
      let query = "SELECT * FROM mappings WHERE 1=1";
      const params = [];
      if (filters?.import_id) {
        query += " AND import_id = ?";
        params.push(filters.import_id);
      }
      if (filters?.csv_column) {
        query += " AND csv_column LIKE ?";
        params.push(`%${filters.csv_column}%`);
      }
      if (filters?.woodpecker_field) {
        query += " AND woodpecker_field = ?";
        params.push(filters.woodpecker_field);
      }
      if (filters?.mapping_type) {
        query += " AND mapping_type = ?";
        params.push(filters.mapping_type);
      }
      if (filters?.is_active !== void 0) {
        query += " AND is_active = ?";
        params.push(filters.is_active);
      }
      if (filters?.dateFrom) {
        query += " AND created_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND created_at <= ?";
        params.push(filters.dateTo);
      }
      query += " ORDER BY created_at DESC";
      if (pagination?.limit) {
        query += " LIMIT ?";
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += " OFFSET ?";
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static update(id, updates) {
    return withDatabase((db) => {
      const fields = Object.keys(updates).filter((key) => updates[key] !== void 0);
      if (fields.length === 0) return this.getById(id);
      const setClause = fields.map((field) => `${field} = ?`).join(", ");
      const values = fields.map((field) => updates[field]);
      const stmt = db.prepare(`UPDATE mappings SET ${setClause} WHERE id = ?`);
      const result = stmt.run(...values, id);
      return result.changes > 0 ? this.getById(id) : null;
    });
  }
  static updateActiveStatus(id, isActive) {
    return this.update(id, { is_active: isActive });
  }
  static updateMappingType(id, mappingType) {
    return this.update(id, { mapping_type: mappingType });
  }
  static bulkUpdateActiveStatus(ids, isActive) {
    return withTransaction((db) => {
      const placeholders = ids.map(() => "?").join(",");
      const stmt = db.prepare(`UPDATE mappings SET is_active = ? WHERE id IN (${placeholders})`);
      const result = stmt.run(isActive, ...ids);
      return result.changes;
    });
  }
  static delete(id) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM mappings WHERE id = ?");
      const result = stmt.run(id);
      return result.changes > 0;
    });
  }
  static deleteByImport(importId) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM mappings WHERE import_id = ?");
      const result = stmt.run(importId);
      return result.changes;
    });
  }
  static deactivateByImport(importId) {
    return withTransaction((db) => {
      const stmt = db.prepare("UPDATE mappings SET is_active = 0 WHERE import_id = ?");
      const result = stmt.run(importId);
      return result.changes;
    });
  }
  static getCount(filters) {
    return withDatabase((db) => {
      let query = "SELECT COUNT(*) as count FROM mappings WHERE 1=1";
      const params = [];
      if (filters?.import_id) {
        query += " AND import_id = ?";
        params.push(filters.import_id);
      }
      if (filters?.csv_column) {
        query += " AND csv_column LIKE ?";
        params.push(`%${filters.csv_column}%`);
      }
      if (filters?.woodpecker_field) {
        query += " AND woodpecker_field = ?";
        params.push(filters.woodpecker_field);
      }
      if (filters?.mapping_type) {
        query += " AND mapping_type = ?";
        params.push(filters.mapping_type);
      }
      if (filters?.is_active !== void 0) {
        query += " AND is_active = ?";
        params.push(filters.is_active);
      }
      if (filters?.dateFrom) {
        query += " AND created_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND created_at <= ?";
        params.push(filters.dateTo);
      }
      const stmt = db.prepare(query);
      const result = stmt.get(...params);
      return result.count;
    });
  }
  static getByImport(importId, activeOnly = true) {
    const filters = { import_id: importId };
    if (activeOnly) {
      filters.is_active = true;
    }
    return this.getAll(filters);
  }
  static getActiveMappings(importId) {
    const filters = { is_active: true };
    if (importId) {
      filters.import_id = importId;
    }
    return this.getAll(filters);
  }
  static getByMappingType(mappingType, pagination) {
    return this.getAll({ mapping_type: mappingType }, pagination);
  }
  static getByWoodpeckerField(woodpeckerField) {
    return this.getAll({ woodpecker_field: woodpeckerField });
  }
  static searchByCsvColumn(csvColumn) {
    return this.getAll({ csv_column: csvColumn });
  }
  static getMappingConfiguration(importId) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        SELECT csv_column, woodpecker_field 
        FROM mappings 
        WHERE import_id = ? AND is_active = 1
      `);
      const results = stmt.all(importId);
      return results.reduce((config, row) => {
        config[row.csv_column] = row.woodpecker_field;
        return config;
      }, {});
    });
  }
  static getWoodpeckerFieldUsage() {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        SELECT woodpecker_field, COUNT(*) as usage_count 
        FROM mappings 
        WHERE is_active = 1 
        GROUP BY woodpecker_field 
        ORDER BY usage_count DESC
      `);
      const results = stmt.all();
      return results.reduce((usage, row) => {
        usage[row.woodpecker_field] = row.usage_count;
        return usage;
      }, {});
    });
  }
  static getMappingStats() {
    return withDatabase((db) => {
      const totalStmt = db.prepare("SELECT COUNT(*) as count FROM mappings");
      const total = totalStmt.get().count;
      const activeStmt = db.prepare("SELECT COUNT(*) as count FROM mappings WHERE is_active = 1");
      const active = activeStmt.get().count;
      const typeStmt = db.prepare("SELECT mapping_type, COUNT(*) as count FROM mappings GROUP BY mapping_type");
      const typeResults = typeStmt.all();
      const byType = typeResults.reduce((acc, row) => {
        acc[row.mapping_type] = row.count;
        return acc;
      }, {});
      return { total, active, byType };
    });
  }
  static duplicateImportMappings(sourceImportId, targetImportId) {
    return withTransaction((db) => {
      const sourceMappings = this.getByImport(sourceImportId, true);
      const bulkData = {
        import_id: targetImportId,
        mappings: sourceMappings.map((mapping) => ({
          csv_column: mapping.csv_column,
          woodpecker_field: mapping.woodpecker_field,
          mapping_type: mapping.mapping_type,
          is_active: mapping.is_active
        }))
      };
      return this.bulkCreate(bulkData);
    });
  }
  static validateMappingConfiguration(importId) {
    return withDatabase((db) => {
      const mappings = this.getByImport(importId, true);
      const errors = [];
      if (mappings.length === 0) {
        errors.push("No active mappings found for import");
      }
      const csvColumns = /* @__PURE__ */ new Set();
      const woodpeckerFields = /* @__PURE__ */ new Set();
      for (const mapping of mappings) {
        if (csvColumns.has(mapping.csv_column)) {
          errors.push(`Duplicate CSV column mapping: ${mapping.csv_column}`);
        }
        csvColumns.add(mapping.csv_column);
        if (woodpeckerFields.has(mapping.woodpecker_field)) {
          errors.push(`Duplicate Woodpecker field mapping: ${mapping.woodpecker_field}`);
        }
        woodpeckerFields.add(mapping.woodpecker_field);
      }
      return {
        valid: errors.length === 0,
        errors
      };
    });
  }
}
class AppMetadataDAL {
  static create(key, value) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        INSERT INTO app_metadata (key, value, updated_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(key) DO UPDATE SET 
          value = excluded.value,
          updated_at = CURRENT_TIMESTAMP
      `);
      stmt.run(key, value);
      return this.getByKey(key);
    });
  }
  static bulkCreate(metadata) {
    return withTransaction((db) => {
      const stmt = db.prepare(`
        INSERT INTO app_metadata (key, value, updated_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(key) DO UPDATE SET 
          value = excluded.value,
          updated_at = CURRENT_TIMESTAMP
      `);
      const results = [];
      for (const [key, value] of Object.entries(metadata)) {
        stmt.run(key, value);
        const created = this.getByKey(key);
        if (created) results.push(created);
      }
      return results;
    });
  }
  static getByKey(key) {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT * FROM app_metadata WHERE key = ?");
      return stmt.get(key) || null;
    });
  }
  static getValue(key) {
    const record = this.getByKey(key);
    return record?.value || null;
  }
  static getAll(filters, pagination) {
    return withDatabase((db) => {
      let query = "SELECT * FROM app_metadata WHERE 1=1";
      const params = [];
      if (filters?.keyPattern) {
        query += " AND key LIKE ?";
        params.push(`%${filters.keyPattern}%`);
      }
      if (filters?.valuePattern) {
        query += " AND value LIKE ?";
        params.push(`%${filters.valuePattern}%`);
      }
      if (filters?.dateFrom) {
        query += " AND updated_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND updated_at <= ?";
        params.push(filters.dateTo);
      }
      query += " ORDER BY updated_at DESC";
      if (pagination?.limit) {
        query += " LIMIT ?";
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += " OFFSET ?";
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static update(key, value) {
    return this.create(key, value);
  }
  static delete(key) {
    return withDatabase((db) => {
      const stmt = db.prepare("DELETE FROM app_metadata WHERE key = ?");
      const result = stmt.run(key);
      return result.changes > 0;
    });
  }
  static bulkDelete(keys) {
    return withTransaction((db) => {
      const placeholders = keys.map(() => "?").join(",");
      const stmt = db.prepare(`DELETE FROM app_metadata WHERE key IN (${placeholders})`);
      const result = stmt.run(...keys);
      return result.changes;
    });
  }
  static getCount(filters) {
    return withDatabase((db) => {
      let query = "SELECT COUNT(*) as count FROM app_metadata WHERE 1=1";
      const params = [];
      if (filters?.keyPattern) {
        query += " AND key LIKE ?";
        params.push(`%${filters.keyPattern}%`);
      }
      if (filters?.valuePattern) {
        query += " AND value LIKE ?";
        params.push(`%${filters.valuePattern}%`);
      }
      if (filters?.dateFrom) {
        query += " AND updated_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND updated_at <= ?";
        params.push(filters.dateTo);
      }
      const stmt = db.prepare(query);
      const result = stmt.get(...params);
      return result.count;
    });
  }
  static getByKeyPattern(pattern) {
    return this.getAll({ keyPattern: pattern });
  }
  static getByValuePattern(pattern) {
    return this.getAll({ valuePattern: pattern });
  }
  static getAllAsObject() {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT key, value FROM app_metadata");
      const results = stmt.all();
      return results.reduce((obj, row) => {
        obj[row.key] = row.value;
        return obj;
      }, {});
    });
  }
  static getConfiguration() {
    return this.getByKeyPattern("config_").reduce((config, record) => {
      const configKey = record.key.replace("config_", "");
      config[configKey] = record.value;
      return config;
    }, {});
  }
  static setConfiguration(config) {
    const prefixedConfig = Object.entries(config).reduce((acc, [key, value]) => {
      acc[`config_${key}`] = value;
      return acc;
    }, {});
    return this.bulkCreate(prefixedConfig);
  }
  static getSchemaVersion() {
    return this.getValue("schema_version");
  }
  static setSchemaVersion(version) {
    return this.create("schema_version", version);
  }
  static getLastMigration() {
    return this.getValue("last_migration");
  }
  static setLastMigration(migration) {
    return this.create("last_migration", migration);
  }
  static getAppVersion() {
    return this.getValue("app_version");
  }
  static setAppVersion(version) {
    return this.create("app_version", version);
  }
  static getInstallationId() {
    return this.getValue("installation_id");
  }
  static setInstallationId(id) {
    return this.create("installation_id", id);
  }
  static getCreatedAt() {
    return this.getValue("created_at");
  }
  static setCreatedAt(timestamp) {
    return this.create("created_at", timestamp);
  }
  static getLastBackup() {
    return this.getValue("last_backup");
  }
  static setLastBackup(timestamp) {
    return this.create("last_backup", timestamp);
  }
  static getFeatureFlags() {
    const flags = this.getByKeyPattern("feature_");
    return flags.reduce((features, record) => {
      const featureName = record.key.replace("feature_", "");
      features[featureName] = record.value.toLowerCase() === "true";
      return features;
    }, {});
  }
  static setFeatureFlag(feature, enabled) {
    return this.create(`feature_${feature}`, enabled.toString());
  }
  static setFeatureFlags(flags) {
    const prefixedFlags = Object.entries(flags).reduce((acc, [key, value]) => {
      acc[`feature_${key}`] = value.toString();
      return acc;
    }, {});
    return this.bulkCreate(prefixedFlags);
  }
  static getUserPreferences() {
    const prefs = this.getByKeyPattern("pref_");
    return prefs.reduce((preferences, record) => {
      const prefName = record.key.replace("pref_", "");
      preferences[prefName] = record.value;
      return preferences;
    }, {});
  }
  static setUserPreference(preference, value) {
    return this.create(`pref_${preference}`, value);
  }
  static setUserPreferences(preferences) {
    const prefixedPrefs = Object.entries(preferences).reduce((acc, [key, value]) => {
      acc[`pref_${key}`] = value;
      return acc;
    }, {});
    return this.bulkCreate(prefixedPrefs);
  }
  static getStats() {
    const stats = this.getByKeyPattern("stat_");
    return stats.reduce((statistics, record) => {
      const statName = record.key.replace("stat_", "");
      statistics[statName] = record.value;
      return statistics;
    }, {});
  }
  static setStat(stat, value) {
    return this.create(`stat_${stat}`, value.toString());
  }
  static incrementStat(stat, increment = 1) {
    const currentValue = this.getValue(`stat_${stat}`);
    const newValue = (parseInt(currentValue || "0", 10) + increment).toString();
    return this.create(`stat_${stat}`, newValue);
  }
  static clearByPrefix(prefix) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM app_metadata WHERE key LIKE ?");
      const result = stmt.run(`${prefix}%`);
      return result.changes;
    });
  }
  static backup() {
    this.setLastBackup((/* @__PURE__ */ new Date()).toISOString());
    return this.getAll();
  }
  static restore(backup) {
    return withTransaction((db) => {
      db.prepare("DELETE FROM app_metadata").run();
      const stmt = db.prepare(`
        INSERT INTO app_metadata (key, value, updated_at)
        VALUES (?, ?, ?)
      `);
      let restored = 0;
      for (const record of backup) {
        stmt.run(record.key, record.value, record.updated_at || (/* @__PURE__ */ new Date()).toISOString());
        restored++;
      }
      return restored;
    });
  }
}
class AdvancedQueriesDAL {
  // Complex joins between tables
  static getImportsWithStats(pagination) {
    return withDatabase((db) => {
      let query = `
        SELECT 
          i.*,
          COALESCE(l.actual_lead_count, 0) as actual_lead_count,
          COALESCE(c.content_count, 0) as content_count,
          COALESCE(m.mapping_count, 0) as mapping_count
        FROM imports i
        LEFT JOIN (
          SELECT import_id, COUNT(*) as actual_lead_count
          FROM leads
          GROUP BY import_id
        ) l ON i.id = l.import_id
        LEFT JOIN (
          SELECT l.import_id, COUNT(gc.*) as content_count
          FROM leads l
          LEFT JOIN generated_content gc ON l.id = gc.lead_id
          GROUP BY l.import_id
        ) c ON i.id = c.import_id
        LEFT JOIN (
          SELECT import_id, COUNT(*) as mapping_count
          FROM mappings
          WHERE is_active = 1
          GROUP BY import_id
        ) m ON i.id = m.import_id
        ORDER BY i.import_date DESC
      `;
      if (pagination?.limit) {
        query += ` LIMIT ${pagination.limit}`;
        if (pagination?.offset) {
          query += ` OFFSET ${pagination.offset}`;
        }
      }
      const stmt = db.prepare(query);
      return stmt.all();
    });
  }
  static getLeadsWithContent(importId, pagination) {
    return withDatabase((db) => {
      let query = `
        SELECT 
          l.*,
          json_group_array(
            json_object(
              'id', gc.id,
              'touchpoint_number', gc.touchpoint_number,
              'content', gc.content,
              'content_type', gc.content_type,
              'template_id', gc.template_id,
              'status', gc.status,
              'generated_at', gc.generated_at,
              'approved_at', gc.approved_at
            )
          ) FILTER (WHERE gc.id IS NOT NULL) as content_items_json
        FROM leads l
        LEFT JOIN generated_content gc ON l.id = gc.lead_id
      `;
      const params = [];
      if (importId) {
        query += ` WHERE l.import_id = ?`;
        params.push(importId);
      }
      query += ` GROUP BY l.id ORDER BY l.created_at DESC`;
      if (pagination?.limit) {
        query += ` LIMIT ?`;
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += ` OFFSET ?`;
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      const results = stmt.all(...params);
      return results.map((row) => ({
        ...row,
        content_items: row.content_items_json ? JSON.parse(row.content_items_json) : []
      }));
    });
  }
  static getLeadsWithImportInfo(filters, pagination) {
    return withDatabase((db) => {
      let query = `
        SELECT 
          l.*,
          i.filename as import_filename,
          i.import_date,
          i.status as import_status
        FROM leads l
        INNER JOIN imports i ON l.import_id = i.id
        WHERE 1=1
      `;
      const params = [];
      if (filters?.importId) {
        query += ` AND l.import_id = ?`;
        params.push(filters.importId);
      }
      if (filters?.status) {
        query += ` AND l.status = ?`;
        params.push(filters.status);
      }
      if (filters?.dateFrom) {
        query += ` AND l.created_at >= ?`;
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += ` AND l.created_at <= ?`;
        params.push(filters.dateTo);
      }
      if (filters?.query) {
        query += ` AND (
          l.company LIKE ? OR 
          l.contact_name LIKE ? OR 
          l.email LIKE ? OR
          i.filename LIKE ?
        )`;
        const searchTerm = `%${filters.query}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }
      query += ` ORDER BY l.created_at DESC`;
      if (pagination?.limit) {
        query += ` LIMIT ?`;
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += ` OFFSET ?`;
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static getContentWithLeadInfo(filters, pagination) {
    return withDatabase((db) => {
      let query = `
        SELECT 
          gc.*,
          l.company as lead_company,
          l.contact_name as lead_contact_name,
          l.email as lead_email,
          i.filename as import_filename
        FROM generated_content gc
        INNER JOIN leads l ON gc.lead_id = l.id
        INNER JOIN imports i ON l.import_id = i.id
        WHERE 1=1
      `;
      const params = [];
      if (filters?.status) {
        query += ` AND gc.status = ?`;
        params.push(filters.status);
      }
      if (filters?.dateFrom) {
        query += ` AND gc.generated_at >= ?`;
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += ` AND gc.generated_at <= ?`;
        params.push(filters.dateTo);
      }
      if (filters?.query) {
        query += ` AND (
          gc.content LIKE ? OR 
          l.company LIKE ? OR 
          l.contact_name LIKE ? OR 
          l.email LIKE ?
        )`;
        const searchTerm = `%${filters.query}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }
      query += ` ORDER BY gc.generated_at DESC`;
      if (pagination?.limit) {
        query += ` LIMIT ?`;
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += ` OFFSET ?`;
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  // Aggregation queries for reporting
  static getReportingStats(dateFrom, dateTo) {
    return withDatabase((db) => {
      let dateFilter = "";
      const params = [];
      if (dateFrom && dateTo) {
        dateFilter = "WHERE created_at >= ? AND created_at <= ?";
        params.push(dateFrom, dateTo);
      } else if (dateFrom) {
        dateFilter = "WHERE created_at >= ?";
        params.push(dateFrom);
      } else if (dateTo) {
        dateFilter = "WHERE created_at <= ?";
        params.push(dateTo);
      }
      const totalImports = db.prepare(`SELECT COUNT(*) as count FROM imports ${dateFilter}`).get(...params);
      const totalLeads = db.prepare(`SELECT COUNT(*) as count FROM leads ${dateFilter}`).get(...params);
      const totalContent = db.prepare(`SELECT COUNT(*) as count FROM generated_content ${dateFilter.replace("created_at", "generated_at")}`).get(...params);
      const totalMappings = db.prepare(`SELECT COUNT(*) as count FROM mappings ${dateFilter}`).get(...params);
      const leadsByStatus = db.prepare(`SELECT status, COUNT(*) as count FROM leads ${dateFilter} GROUP BY status`).all(...params);
      const contentByStatus = db.prepare(`SELECT status, COUNT(*) as count FROM generated_content ${dateFilter.replace("created_at", "generated_at")} GROUP BY status`).all(...params);
      const importsByStatus = db.prepare(`SELECT status, COUNT(*) as count FROM imports ${dateFilter.replace("created_at", "import_date")} GROUP BY status`).all(...params);
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1e3).toISOString();
      const recentImports = db.prepare("SELECT COUNT(*) as count FROM imports WHERE import_date >= ?").get(sevenDaysAgo);
      const recentLeads = db.prepare("SELECT COUNT(*) as count FROM leads WHERE created_at >= ?").get(sevenDaysAgo);
      const recentContent = db.prepare("SELECT COUNT(*) as count FROM generated_content WHERE generated_at >= ?").get(sevenDaysAgo);
      return {
        totalImports: totalImports.count,
        totalLeads: totalLeads.count,
        totalContent: totalContent.count,
        totalMappings: totalMappings.count,
        leadsByStatus: leadsByStatus.reduce((acc, row) => {
          acc[row.status] = row.count;
          return acc;
        }, {}),
        contentByStatus: contentByStatus.reduce((acc, row) => {
          acc[row.status] = row.count;
          return acc;
        }, {}),
        importsByStatus: importsByStatus.reduce((acc, row) => {
          acc[row.status] = row.count;
          return acc;
        }, {}),
        recentActivity: {
          imports: recentImports.count,
          leads: recentLeads.count,
          content: recentContent.count
        }
      };
    });
  }
  // Search functionality across leads
  static searchLeads(query, pagination) {
    return this.getLeadsWithImportInfo({ query }, pagination);
  }
  static searchContent(query, pagination) {
    return this.getContentWithLeadInfo({ query }, pagination);
  }
  // Export query functions
  static getExportData(importId) {
    return withDatabase((db) => {
      const importStmt = db.prepare("SELECT * FROM imports WHERE id = ?");
      const importRecord = importStmt.get(importId);
      const leadsStmt = db.prepare("SELECT * FROM leads WHERE import_id = ? ORDER BY created_at");
      const leads = leadsStmt.all(importId);
      const mappingsStmt = db.prepare("SELECT * FROM mappings WHERE import_id = ? ORDER BY created_at");
      const mappings = mappingsStmt.all(importId);
      const leadIds = leads.map((lead) => lead.id).filter(Boolean);
      let content = [];
      if (leadIds.length > 0) {
        const placeholders = leadIds.map(() => "?").join(",");
        const contentStmt = db.prepare(`SELECT * FROM generated_content WHERE lead_id IN (${placeholders}) ORDER BY lead_id, touchpoint_number`);
        content = contentStmt.all(...leadIds);
      }
      return {
        import: importRecord,
        leads,
        mappings,
        content
      };
    });
  }
  static getLeadsForExport(filters) {
    return withDatabase((db) => {
      let query = `
        SELECT 
          l.*,
          json_group_array(
            json_object(
              'id', gc.id,
              'touchpoint_number', gc.touchpoint_number,
              'content', gc.content,
              'content_type', gc.content_type,
              'status', gc.status,
              'generated_at', gc.generated_at,
              'approved_at', gc.approved_at
            )
          ) FILTER (WHERE gc.id IS NOT NULL) as content_items_json
        FROM leads l
        LEFT JOIN generated_content gc ON l.id = gc.lead_id
        WHERE 1=1
      `;
      const params = [];
      if (filters?.importId) {
        query += ` AND l.import_id = ?`;
        params.push(filters.importId);
      }
      if (filters?.status) {
        query += ` AND l.status = ?`;
        params.push(filters.status);
      }
      if (filters?.hasContent) {
        query += ` AND EXISTS (SELECT 1 FROM generated_content WHERE lead_id = l.id)`;
      }
      query += ` GROUP BY l.id ORDER BY l.created_at`;
      const stmt = db.prepare(query);
      const results = stmt.all(...params);
      return results.map((row) => ({
        ...row,
        content_items: row.content_items_json ? JSON.parse(row.content_items_json) : []
      }));
    });
  }
  // Performance and analytics queries
  static getPerformanceMetrics() {
    return withDatabase((db) => {
      const avgLeadsStmt = db.prepare(`
        SELECT AVG(lead_count) as avg_leads
        FROM (
          SELECT COUNT(*) as lead_count
          FROM leads
          GROUP BY import_id
        )
      `);
      const avgLeads = avgLeadsStmt.get();
      const avgContentStmt = db.prepare(`
        SELECT AVG(content_count) as avg_content
        FROM (
          SELECT COUNT(*) as content_count
          FROM generated_content
          GROUP BY lead_id
        )
      `);
      const avgContent = avgContentStmt.get();
      const approvalRateStmt = db.prepare(`
        SELECT 
          (COUNT(CASE WHEN status = 'approved' THEN 1 END) * 100.0 / COUNT(*)) as approval_rate
        FROM generated_content
      `);
      const approvalRate = approvalRateStmt.get();
      const topMappingsStmt = db.prepare(`
        SELECT woodpecker_field, COUNT(*) as usage_count
        FROM mappings
        WHERE is_active = 1
        GROUP BY woodpecker_field
        ORDER BY usage_count DESC
        LIMIT 10
      `);
      const topMappings = topMappingsStmt.all();
      const topCompaniesStmt = db.prepare(`
        SELECT company, COUNT(*) as lead_count
        FROM leads
        WHERE company IS NOT NULL AND company != ''
        GROUP BY company
        ORDER BY lead_count DESC
        LIMIT 10
      `);
      const topCompanies = topCompaniesStmt.all();
      return {
        avgLeadsPerImport: Math.round((avgLeads.avg_leads || 0) * 100) / 100,
        avgContentPerLead: Math.round((avgContent.avg_content || 0) * 100) / 100,
        contentApprovalRate: Math.round((approvalRate.approval_rate || 0) * 100) / 100,
        mostUsedMappings: topMappings,
        topCompanies
      };
    });
  }
}
class DALError extends Error {
  constructor(message, operation, table, originalError) {
    super(message);
    this.operation = operation;
    this.table = table;
    this.originalError = originalError;
    this.name = "DALError";
  }
}
class ValidationError extends DALError {
  constructor(message, operation, table, field, value) {
    super(message, operation, table);
    this.field = field;
    this.value = value;
    this.name = "ValidationError";
  }
}
class NotFoundError extends DALError {
  constructor(operation, table, id) {
    super(`Record not found: ${table} with id ${id}`, operation, table);
    this.id = id;
    this.name = "NotFoundError";
  }
}
class ForeignKeyError extends DALError {
  constructor(operation, table, foreignKey, foreignValue) {
    super(`Foreign key constraint failed: ${foreignKey} = ${foreignValue}`, operation, table);
    this.foreignKey = foreignKey;
    this.foreignValue = foreignValue;
    this.name = "ForeignKeyError";
  }
}
class UniqueConstraintError extends DALError {
  constructor(operation, table, field, value) {
    super(`Unique constraint violation: ${field} = ${value}`, operation, table);
    this.field = field;
    this.value = value;
    this.name = "UniqueConstraintError";
  }
}
class TransactionError extends DALError {
  constructor(message, operation, originalError) {
    super(message, operation, "transaction", originalError);
    this.name = "TransactionError";
  }
}
function handleIpcError(error, operation) {
  logger.error("IPC", `Error in ${operation}`, error instanceof Error ? error : new Error(String(error)));
  if (error instanceof ValidationError) {
    return {
      success: false,
      error: {
        type: "ValidationError",
        message: error.message,
        code: "VALIDATION_FAILED",
        details: error.details
      }
    };
  }
  if (error instanceof NotFoundError) {
    return {
      success: false,
      error: {
        type: "NotFoundError",
        message: error.message,
        code: "NOT_FOUND"
      }
    };
  }
  if (error instanceof ForeignKeyError) {
    return {
      success: false,
      error: {
        type: "ForeignKeyError",
        message: error.message,
        code: "FOREIGN_KEY_CONSTRAINT"
      }
    };
  }
  if (error instanceof UniqueConstraintError) {
    return {
      success: false,
      error: {
        type: "UniqueConstraintError",
        message: error.message,
        code: "UNIQUE_CONSTRAINT"
      }
    };
  }
  if (error instanceof TransactionError) {
    return {
      success: false,
      error: {
        type: "TransactionError",
        message: error.message,
        code: "TRANSACTION_FAILED"
      }
    };
  }
  if (error instanceof DALError) {
    return {
      success: false,
      error: {
        type: "DALError",
        message: error.message,
        code: "DATABASE_ERROR"
      }
    };
  }
  const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
  return {
    success: false,
    error: {
      type: "UnknownError",
      message: errorMessage,
      code: "UNKNOWN_ERROR"
    }
  };
}
function validateInput(data, requiredFields) {
  if (!data || typeof data !== "object") {
    throw new ValidationError("Input data is required and must be an object");
  }
  const missingFields = requiredFields.filter((field) => {
    const value = data[field];
    return value === void 0 || value === null || value === "";
  });
  if (missingFields.length > 0) {
    throw new ValidationError(
      `Missing required fields: ${missingFields.join(", ")}`,
      { missingFields }
    );
  }
}
function setupImportsHandlers() {
  ipcMain.handle("ipc:imports:create", async (_, data) => {
    try {
      validateInput(data, ["filename", "file_path", "status"]);
      return ImportsDAL.create(data);
    } catch (error) {
      return handleIpcError(error, "imports:create");
    }
  });
  ipcMain.handle("ipc:imports:getAll", async (_, options) => {
    try {
      return ImportsDAL.getAll(options);
    } catch (error) {
      return handleIpcError(error, "imports:getAll");
    }
  });
  ipcMain.handle("ipc:imports:getById", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return ImportsDAL.getById(id);
    } catch (error) {
      return handleIpcError(error, "imports:getById");
    }
  });
  ipcMain.handle("ipc:imports:update", async (_, id, data) => {
    try {
      validateInput({ id }, ["id"]);
      return ImportsDAL.update(id, data);
    } catch (error) {
      return handleIpcError(error, "imports:update");
    }
  });
  ipcMain.handle("ipc:imports:delete", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return ImportsDAL.delete(id);
    } catch (error) {
      return handleIpcError(error, "imports:delete");
    }
  });
  console.log("Imports IPC handlers setup complete");
}
function setupLeadsHandlers() {
  ipcMain.handle("ipc:leads:create", async (_, data) => {
    try {
      validateInput(data, ["import_id", "email"]);
      return LeadsDAL.create(data);
    } catch (error) {
      return handleIpcError(error, "leads:create");
    }
  });
  ipcMain.handle("ipc:leads:bulkCreate", async (_, leads) => {
    try {
      validateInput({ leads }, ["leads"]);
      if (!Array.isArray(leads) || leads.length === 0) {
        throw new Error("Leads array is required and must not be empty");
      }
      return LeadsDAL.bulkCreate(leads);
    } catch (error) {
      return handleIpcError(error, "leads:bulkCreate");
    }
  });
  ipcMain.handle("ipc:leads:getAll", async (_, options) => {
    try {
      return LeadsDAL.getAll(options);
    } catch (error) {
      return handleIpcError(error, "leads:getAll");
    }
  });
  ipcMain.handle("ipc:leads:getById", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return LeadsDAL.getById(id);
    } catch (error) {
      return handleIpcError(error, "leads:getById");
    }
  });
  ipcMain.handle("ipc:leads:getByImport", async (_, importId) => {
    try {
      validateInput({ importId }, ["importId"]);
      return LeadsDAL.getByImport(importId);
    } catch (error) {
      return handleIpcError(error, "leads:getByImport");
    }
  });
  ipcMain.handle("ipc:leads:update", async (_, id, data) => {
    try {
      validateInput({ id }, ["id"]);
      return LeadsDAL.update(id, data);
    } catch (error) {
      return handleIpcError(error, "leads:update");
    }
  });
  ipcMain.handle("ipc:leads:delete", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return LeadsDAL.delete(id);
    } catch (error) {
      return handleIpcError(error, "leads:delete");
    }
  });
  ipcMain.handle("ipc:leads:search", async (_, query, options) => {
    try {
      validateInput({ query }, ["query"]);
      return LeadsDAL.search(query, options);
    } catch (error) {
      return handleIpcError(error, "leads:search");
    }
  });
  console.log("Leads IPC handlers setup complete");
}
function setupGeneratedContentHandlers() {
  ipcMain.handle("ipc:content:create", async (_, data) => {
    try {
      validateInput(data, ["lead_id", "touchpoint_number", "content_type"]);
      return GeneratedContentDAL.create(data);
    } catch (error) {
      return handleIpcError(error, "content:create");
    }
  });
  ipcMain.handle("ipc:content:getByLead", async (_, leadId) => {
    try {
      validateInput({ leadId }, ["leadId"]);
      return GeneratedContentDAL.getByLead(leadId);
    } catch (error) {
      return handleIpcError(error, "content:getByLead");
    }
  });
  ipcMain.handle("ipc:content:getByTouchpoint", async (_, touchpoint, options) => {
    try {
      validateInput({ touchpoint }, ["touchpoint"]);
      return GeneratedContentDAL.getByTouchpoint(touchpoint, options);
    } catch (error) {
      return handleIpcError(error, "content:getByTouchpoint");
    }
  });
  ipcMain.handle("ipc:content:update", async (_, id, data) => {
    try {
      validateInput({ id }, ["id"]);
      return GeneratedContentDAL.update(id, data);
    } catch (error) {
      return handleIpcError(error, "content:update");
    }
  });
  ipcMain.handle("ipc:content:delete", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return GeneratedContentDAL.delete(id);
    } catch (error) {
      return handleIpcError(error, "content:delete");
    }
  });
  console.log("Generated Content IPC handlers setup complete");
}
function setupMappingsHandlers() {
  ipcMain.handle("ipc:mappings:create", async (_, data) => {
    try {
      validateInput(data, ["import_id", "source_field", "target_field"]);
      return MappingsDAL.create(data);
    } catch (error) {
      return handleIpcError(error, "mappings:create");
    }
  });
  ipcMain.handle("ipc:mappings:getByImport", async (_, importId) => {
    try {
      validateInput({ importId }, ["importId"]);
      return MappingsDAL.getByImport(importId);
    } catch (error) {
      return handleIpcError(error, "mappings:getByImport");
    }
  });
  ipcMain.handle("ipc:mappings:getActive", async (_, options) => {
    try {
      return MappingsDAL.getActive(options);
    } catch (error) {
      return handleIpcError(error, "mappings:getActive");
    }
  });
  ipcMain.handle("ipc:mappings:update", async (_, id, data) => {
    try {
      validateInput({ id }, ["id"]);
      return MappingsDAL.update(id, data);
    } catch (error) {
      return handleIpcError(error, "mappings:update");
    }
  });
  ipcMain.handle("ipc:mappings:delete", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return MappingsDAL.delete(id);
    } catch (error) {
      return handleIpcError(error, "mappings:delete");
    }
  });
  console.log("Mappings IPC handlers setup complete");
}
function setupAppMetadataHandlers() {
  ipcMain.handle("ipc:metadata:get", async (_, key) => {
    try {
      validateInput({ key }, ["key"]);
      return AppMetadataDAL.get(key);
    } catch (error) {
      return handleIpcError(error, "metadata:get");
    }
  });
  ipcMain.handle("ipc:metadata:set", async (_, key, value) => {
    try {
      validateInput({ key, value }, ["key", "value"]);
      return AppMetadataDAL.set(key, value);
    } catch (error) {
      return handleIpcError(error, "metadata:set");
    }
  });
  ipcMain.handle("ipc:metadata:delete", async (_, key) => {
    try {
      validateInput({ key }, ["key"]);
      return AppMetadataDAL.delete(key);
    } catch (error) {
      return handleIpcError(error, "metadata:delete");
    }
  });
  ipcMain.handle("ipc:metadata:getAll", async (_, options) => {
    try {
      return AppMetadataDAL.getAll(options);
    } catch (error) {
      return handleIpcError(error, "metadata:getAll");
    }
  });
  console.log("App Metadata IPC handlers setup complete");
}
function setupAdvancedQueriesHandlers() {
  ipcMain.handle("ipc:queries:getLeadsWithContent", async (_, options) => {
    try {
      return AdvancedQueriesDAL.getLeadsWithContent(options);
    } catch (error) {
      return handleIpcError(error, "queries:getLeadsWithContent");
    }
  });
  ipcMain.handle("ipc:queries:getImportSummary", async (_, importId) => {
    try {
      return AdvancedQueriesDAL.getImportSummary(importId);
    } catch (error) {
      return handleIpcError(error, "queries:getImportSummary");
    }
  });
  ipcMain.handle("ipc:queries:getContentStats", async (_) => {
    try {
      return AdvancedQueriesDAL.getContentStats();
    } catch (error) {
      return handleIpcError(error, "queries:getContentStats");
    }
  });
  ipcMain.handle("ipc:queries:exportData", async (_, format, options) => {
    try {
      validateInput({ format }, ["format"]);
      return AdvancedQueriesDAL.exportData(format, options);
    } catch (error) {
      return handleIpcError(error, "queries:exportData");
    }
  });
  console.log("Advanced Queries IPC handlers setup complete");
}
function setupIpcHandlers() {
  console.log("Setting up IPC handlers...");
  try {
    setupImportsHandlers();
    setupLeadsHandlers();
    setupGeneratedContentHandlers();
    setupMappingsHandlers();
    setupAppMetadataHandlers();
    setupAdvancedQueriesHandlers();
    console.log("All IPC handlers setup successfully");
  } catch (error) {
    console.error("Failed to setup IPC handlers:", error);
    throw error;
  }
}
const __dirname = path$1.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path$1.join(__dirname, "../..");
const MAIN_DIST = path$1.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path$1.join(process.env.APP_ROOT, "dist");
const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path$1.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
if (process.platform === "win32") {
  app.disableHardwareAcceleration();
}
if (process.platform === "win32") {
  app.setAppUserModelId(app.getName());
}
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}
let win = null;
const preload = path$1.join(__dirname, "../preload/preload.mjs");
const indexHtml = path$1.join(RENDERER_DIST, "index.html");
function createApplicationMenu() {
  const template = [
    {
      label: "File",
      submenu: [
        {
          label: "New Import",
          accelerator: "CmdOrCtrl+N",
          click: () => {
            win?.webContents.send("menu-action", "new-import");
          }
        },
        { type: "separator" },
        {
          label: "Exit",
          accelerator: process.platform === "darwin" ? "Cmd+Q" : "Ctrl+Q",
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: "Edit",
      submenu: [
        { role: "undo" },
        { role: "redo" },
        { type: "separator" },
        { role: "cut" },
        { role: "copy" },
        { role: "paste" },
        { role: "selectall" }
      ]
    },
    {
      label: "View",
      submenu: [
        { role: "reload" },
        { role: "forceReload" },
        { role: "toggleDevTools" },
        { type: "separator" },
        { role: "resetZoom" },
        { role: "zoomIn" },
        { role: "zoomOut" },
        { type: "separator" },
        { role: "togglefullscreen" }
      ]
    },
    {
      label: "Window",
      submenu: [
        { role: "minimize" },
        { role: "close" }
      ]
    },
    {
      label: "Help",
      submenu: [
        {
          label: "About Woodpecker API",
          click: () => {
            win?.webContents.send("menu-action", "about");
          }
        },
        {
          label: "Learn More",
          click: () => {
            shell.openExternal("https://github.com/your-repo/woodpecker-api");
          }
        }
      ]
    }
  ];
  if (process.platform === "darwin") {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: "about" },
        { type: "separator" },
        { role: "services" },
        { type: "separator" },
        { role: "hide" },
        { role: "hideOthers" },
        { role: "unhide" },
        { type: "separator" },
        { role: "quit" }
      ]
    });
    template[4].submenu = [
      { role: "close" },
      { role: "minimize" },
      { role: "zoom" },
      { type: "separator" },
      { role: "front" }
    ];
  }
  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}
async function createWindow() {
  win = new BrowserWindow({
    title: "Woodpecker API",
    icon: path$1.join(process.env.VITE_PUBLIC, "favicon.ico"),
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    center: true,
    show: false,
    // Don't show until ready
    titleBarStyle: process.platform === "darwin" ? "hiddenInset" : "default",
    webPreferences: {
      preload,
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      sandbox: false,
      // Required for IPC bridge
      spellcheck: false
    }
  });
  win.once("ready-to-show", () => {
    if (win) {
      win.show();
      if (process.platform === "darwin") {
        win.focus();
      }
    }
  });
  win.on("closed", () => {
    win = null;
  });
  win.on("close", (event) => {
    if (process.platform === "darwin") {
      event.preventDefault();
      win?.hide();
    }
  });
  try {
    if (VITE_DEV_SERVER_URL) {
      await win.loadURL(VITE_DEV_SERVER_URL);
      win.webContents.openDevTools();
    } else {
      await win.loadFile(indexHtml);
    }
  } catch (error) {
    logger.error("Window", "Failed to load application content", error instanceof Error ? error : new Error(String(error)));
    throw error;
  }
  win.webContents.on("did-finish-load", () => {
    logger.info("Window", "Application content loaded successfully");
    win?.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  win.webContents.on("did-fail-load", (event, errorCode, errorDescription) => {
    logger.error("Window", `Failed to load content: ${errorDescription} (${errorCode})`);
  });
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith("https:") || url.startsWith("http:")) {
      shell.openExternal(url);
    }
    return { action: "deny" };
  });
}
app.whenReady().then(async () => {
  try {
    logger.info("App", "Application starting up");
    let dbInitialized = false;
    let retryCount = 0;
    const maxRetries = 3;
    while (!dbInitialized && retryCount < maxRetries) {
      try {
        await initializeDatabase();
        dbInitialized = true;
        logger.info("Database", "Database initialized successfully");
      } catch (dbError) {
        retryCount++;
        logger.error("Database", `Database initialization attempt ${retryCount} failed`, dbError instanceof Error ? dbError : new Error(String(dbError)));
        if (retryCount >= maxRetries) {
          throw new Error(`Failed to initialize database after ${maxRetries} attempts: ${dbError instanceof Error ? dbError.message : String(dbError)}`);
        }
        await new Promise((resolve) => setTimeout(resolve, 1e3 * retryCount));
      }
    }
    setupIpcHandlers();
    logger.info("IPC", "IPC handlers setup complete");
    createApplicationMenu();
    logger.info("App", "Application menu created");
    await createWindow();
    logger.info("App", "Main window created successfully");
    logger.info("App", "Application startup completed successfully");
  } catch (error) {
    logger.error("App", "Failed to initialize application", error instanceof Error ? error : new Error(String(error)));
    dialog.showErrorBox(
      "Application Startup Error",
      `Failed to start Woodpecker API: ${error instanceof Error ? error.message : String(error)}

The application will now exit.`
    );
    app.quit();
  }
});
app.on("window-all-closed", () => {
  logger.info("App", "All windows closed");
  win = null;
  if (process.platform !== "darwin") {
    logger.info("App", "Quitting application");
    app.quit();
  }
});
app.on("second-instance", () => {
  logger.info("App", "Second instance detected, focusing main window");
  if (win) {
    if (win.isMinimized()) {
      win.restore();
    }
    if (!win.isVisible()) {
      win.show();
    }
    win.focus();
  }
});
app.on("activate", () => {
  logger.info("App", "Application activated");
  const allWindows = BrowserWindow.getAllWindows();
  if (allWindows.length) {
    allWindows[0].focus();
  } else {
    createWindow().catch((error) => {
      logger.error("App", "Failed to recreate window on activate", error instanceof Error ? error : new Error(String(error)));
    });
  }
});
app.on("before-quit", (event) => {
  logger.info("App", "Application preparing to quit");
});
app.on("will-quit", (event) => {
  logger.info("App", "Application will quit");
});
app.on("web-contents-created", (_, contents) => {
  logger.debug("Security", "New web contents created, applying security policies");
  contents.on("will-navigate", (navigationEvent, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    const isAllowed = parsedUrl.origin === VITE_DEV_SERVER_URL || navigationUrl.startsWith("file://") || navigationUrl.startsWith("data:");
    if (!isAllowed) {
      logger.warn("Security", `Blocked navigation to: ${navigationUrl}`);
      navigationEvent.preventDefault();
    }
  });
  contents.setWindowOpenHandler(({ url }) => {
    logger.debug("Security", `Window open request for: ${url}`);
    if (url.startsWith("https:") || url.startsWith("http:")) {
      shell.openExternal(url);
      logger.info("Security", `Opened external URL in browser: ${url}`);
    } else {
      logger.warn("Security", `Blocked window open request for: ${url}`);
    }
    return { action: "deny" };
  });
  contents.session.setPermissionRequestHandler((webContents, permission, callback) => {
    logger.warn("Security", `Permission request denied: ${permission}`);
    callback(false);
  });
  contents.on("certificate-error", (event, url, error, certificate, callback) => {
    logger.error("Security", `Certificate error for ${url}: ${error}`);
    event.preventDefault();
    callback(false);
  });
});
let isShuttingDown = false;
async function gracefulShutdown(signal) {
  if (isShuttingDown) {
    logger.warn("App", `Received ${signal} during shutdown, forcing exit`);
    process.exit(1);
  }
  isShuttingDown = true;
  logger.info("App", `Received ${signal}, initiating graceful shutdown`);
  try {
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach((window) => {
      if (!window.isDestroyed()) {
        window.close();
      }
    });
    await new Promise((resolve) => setTimeout(resolve, 1e3));
    logger.info("App", "Graceful shutdown completed");
    app.quit();
  } catch (error) {
    logger.error("App", "Error during graceful shutdown", error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  }
}
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
process.on("uncaughtException", (error) => {
  logger.error("App", "Uncaught exception", error);
  gracefulShutdown("uncaughtException");
});
process.on("unhandledRejection", (reason, promise) => {
  logger.error("App", "Unhandled promise rejection", reason instanceof Error ? reason : new Error(String(reason)));
  gracefulShutdown("unhandledRejection");
});
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
