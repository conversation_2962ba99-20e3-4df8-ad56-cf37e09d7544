import { app } from "electron";
import path from "path";
import fs from "fs";
var LogLevel = /* @__PURE__ */ ((LogLevel2) => {
  LogLevel2[LogLevel2["ERROR"] = 0] = "ERROR";
  LogLevel2[LogLevel2["WARN"] = 1] = "WARN";
  LogLevel2[LogLevel2["INFO"] = 2] = "INFO";
  LogLevel2[LogLevel2["DEBUG"] = 3] = "DEBUG";
  return LogLevel2;
})(LogLevel || {});
class Logger {
  logLevel = 2;
  logFile;
  logStream = null;
  constructor() {
    const userDataPath = app.getPath("userData");
    this.logFile = path.join(userDataPath, "logs", "main.log");
    const logsDir = path.dirname(this.logFile);
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    this.initLogStream();
    if (process.env.NODE_ENV === "development") {
      this.logLevel = 3;
    }
  }
  initLogStream() {
    try {
      this.logStream = fs.createWriteStream(this.logFile, { flags: "a" });
      this.logStream.on("error", (error) => {
        console.error("Log stream error:", error);
      });
    } catch (error) {
      console.error("Failed to initialize log stream:", error);
    }
  }
  formatLogEntry(entry) {
    const { timestamp, level, category, message, data, error } = entry;
    let logLine = `[${timestamp}] ${level.toUpperCase()} [${category}] ${message}`;
    if (data) {
      logLine += ` | Data: ${JSON.stringify(data)}`;
    }
    if (error) {
      logLine += ` | Error: ${error.name}: ${error.message}`;
      if (error.stack) {
        logLine += ` | Stack: ${error.stack}`;
      }
    }
    return logLine;
  }
  writeLog(entry) {
    const logLine = this.formatLogEntry(entry);
    console.log(logLine);
    if (this.logStream) {
      this.logStream.write(logLine + "\n");
    }
  }
  log(level, category, message, data, error) {
    if (level > this.logLevel) {
      return;
    }
    const entry = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      level: LogLevel[level],
      category,
      message,
      data
    };
    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack
      };
    }
    this.writeLog(entry);
  }
  error(category, message, error, data) {
    this.log(0, category, message, data, error);
  }
  warn(category, message, data) {
    this.log(1, category, message, data);
  }
  info(category, message, data) {
    this.log(2, category, message, data);
  }
  debug(category, message, data) {
    this.log(3, category, message, data);
  }
  setLogLevel(level) {
    this.logLevel = level;
    this.info("Logger", `Log level set to ${LogLevel[level]}`);
  }
  getLogLevel() {
    return this.logLevel;
  }
  close() {
    if (this.logStream) {
      this.logStream.end();
      this.logStream = null;
    }
  }
  // Rotate log files to prevent them from getting too large
  rotateLogFile() {
    try {
      if (fs.existsSync(this.logFile)) {
        const stats = fs.statSync(this.logFile);
        const maxSize = 10 * 1024 * 1024;
        if (stats.size > maxSize) {
          const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-");
          const rotatedFile = this.logFile.replace(".log", `-${timestamp}.log`);
          if (this.logStream) {
            this.logStream.end();
          }
          fs.renameSync(this.logFile, rotatedFile);
          this.initLogStream();
          this.info("Logger", `Log file rotated to ${rotatedFile}`);
        }
      }
    } catch (error) {
      console.error("Failed to rotate log file:", error);
    }
  }
}
const logger = new Logger();
setInterval(() => {
  logger.rotateLogFile();
}, 60 * 60 * 1e3);
process.on("exit", () => {
  logger.close();
});
process.on("SIGTERM", () => {
  logger.close();
});
process.on("SIGINT", () => {
  logger.close();
});
export {
  logger as l
};
