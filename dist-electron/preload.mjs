import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";
const electronAPI = {
  imports: {
    create: (data) => ipcRenderer.invoke("ipc:imports:create", data),
    getAll: (options) => ipcRenderer.invoke("ipc:imports:getAll", options),
    getById: (id) => ipcRenderer.invoke("ipc:imports:getById", id),
    update: (id, data) => ipcRenderer.invoke("ipc:imports:update", id, data),
    delete: (id) => ipcRenderer.invoke("ipc:imports:delete", id)
  },
  leads: {
    create: (data) => ipcRenderer.invoke("ipc:leads:create", data),
    bulkCreate: (leads) => ipcRenderer.invoke("ipc:leads:bulkCreate", leads),
    getAll: (options) => ipcRenderer.invoke("ipc:leads:getAll", options),
    getById: (id) => ipc<PERSON>enderer.invoke("ipc:leads:getById", id),
    getByImport: (importId) => ipcRenderer.invoke("ipc:leads:getByImport", importId),
    update: (id, data) => ipcRenderer.invoke("ipc:leads:update", id, data),
    delete: (id) => ipcRenderer.invoke("ipc:leads:delete", id),
    search: (query, options) => ipcRenderer.invoke("ipc:leads:search", query, options)
  },
  content: {
    create: (data) => ipcRenderer.invoke("ipc:content:create", data),
    getByLead: (leadId) => ipcRenderer.invoke("ipc:content:getByLead", leadId),
    getByTouchpoint: (touchpoint, options) => ipcRenderer.invoke("ipc:content:getByTouchpoint", touchpoint, options),
    update: (id, data) => ipcRenderer.invoke("ipc:content:update", id, data),
    delete: (id) => ipcRenderer.invoke("ipc:content:delete", id)
  },
  mappings: {
    create: (data) => ipcRenderer.invoke("ipc:mappings:create", data),
    getByImport: (importId) => ipcRenderer.invoke("ipc:mappings:getByImport", importId),
    getActive: (options) => ipcRenderer.invoke("ipc:mappings:getActive", options),
    update: (id, data) => ipcRenderer.invoke("ipc:mappings:update", id, data),
    delete: (id) => ipcRenderer.invoke("ipc:mappings:delete", id)
  },
  metadata: {
    get: (key) => ipcRenderer.invoke("ipc:metadata:get", key),
    set: (key, value) => ipcRenderer.invoke("ipc:metadata:set", key, value),
    delete: (key) => ipcRenderer.invoke("ipc:metadata:delete", key),
    getAll: (options) => ipcRenderer.invoke("ipc:metadata:getAll", options)
  },
  queries: {
    getLeadsWithContent: (options) => ipcRenderer.invoke("ipc:queries:getLeadsWithContent", options),
    getImportSummary: (importId) => ipcRenderer.invoke("ipc:queries:getImportSummary", importId),
    getContentStats: () => ipcRenderer.invoke("ipc:queries:getContentStats"),
    exportData: (format, options) => ipcRenderer.invoke("ipc:queries:exportData", format, options)
  }
};
contextBridge.exposeInMainWorld("api", electronAPI);
contextBridge.exposeInMainWorld("electronUtils", {
  platform: process.platform,
  versions: process.versions
});
